# WPF功能文档：离线授权激活模块

## 1. 功能概述
- **功能名称**：离线授权激活模块
- **功能描述**：基于设备指纹的离线授权系统，实现License文件导入、功能模块授权控制和授权状态显示。该模块通过收集硬件信息生成唯一设备标识，验证加密的License文件，动态控制各功能模块的访问权限，为整个应用程序提供安全的授权基础。
- **目标用户**：空调系统技术人员、维护工程师、系统管理员
- **优先级**：高（第一阶段开发）
- **预估工作量**：8个工作日
- **依赖的WPF技术**：数据绑定、命令绑定、文件对话框、消息提示、MVVM模式、异步编程

## 2. WPF功能需求详解
### 2.1 核心功能点
- **设备指纹生成**：收集CPU ID、硬盘序列号、MAC地址、主板序列号等硬件信息，使用SHA256算法生成唯一设备标识
- **License文件验证**：导入并验证加密的License文件，检查数字签名、设备指纹匹配、有效期等
- **功能模块权限控制**：根据License内容动态启用/禁用功能模块（外机监控、内机监控、数据回放等9个核心功能）
- **授权状态显示**：实时显示各功能模块的授权状态、License到期时间、剩余天数等信息
- **License文件管理**：支持License文件导入、导出、备份和更新操作

### 2.2 用户操作流程
1. **程序启动检查**：应用程序启动时自动检查License文件有效性和功能模块授权状态
2. **License导入**：用户通过文件对话框选择并导入License文件
3. **设备指纹验证**：系统自动生成当前设备指纹并与License中的指纹进行匹配验证
4. **授权状态查看**：用户查看各功能模块的授权状态、权限级别和到期时间
5. **功能模块访问控制**：系统根据授权状态动态启用/禁用相应功能模块的界面和功能
6. **License更新**：支持导入新的License文件更新授权状态

### 2.3 数据流和绑定规范
- **输入数据**：License文件、硬件信息、功能模块配置、授权策略配置
- **输出数据**：设备指纹、授权状态信息、功能模块权限列表、License验证结果
- **数据绑定**：授权状态绑定到ObservableCollection<FunctionModuleStatus>，License信息绑定到LicenseInfo模型
- **命令绑定**：导入License、刷新授权状态、查看详情等操作绑定到ICommand接口

## 3. WPF界面设计规范
### 3.1 XAML布局设计
- **主容器**：使用Grid作为主布局容器，分为License信息区域、功能模块状态区域、操作按钮区域
- **License信息区域**：使用GroupBox显示License基本信息（设备指纹、有效期、授权级别等）
- **功能模块状态区域**：使用TreeView显示功能模块授权层次结构和状态
- **操作按钮区域**：使用StackPanel水平排列操作按钮（导入License、刷新状态、查看详情）
- **状态栏区域**：使用StatusBar显示授权验证状态和提示信息

### 3.2 视觉设计要求
- **主题风格**：使用WPF控件默认样式，保持标准Windows应用程序外观
- **色彩方案**：使用SystemColors定义的系统颜色，授权状态使用绿色(已授权)、红色(未授权)、黄色(即将到期)
- **字体规范**：使用SystemFonts.DefaultFont，重要信息使用粗体显示
- **间距规范**：控件间距使用8px网格系统，内边距4px，外边距8px

### 3.3 WPF控件设计
- **按钮控件**：使用标准Button控件，通过Command绑定操作，支持启用/禁用状态
- **信息显示控件**：使用TextBlock显示License信息，使用TreeView显示功能模块层次结构
- **状态指示控件**：使用Ellipse显示授权状态，使用ProgressBar显示License剩余时间百分比
- **文件操作控件**：使用OpenFileDialog选择License文件，使用MessageBox显示操作结果

## 4. WPF交互设计
### 4.1 用户操作流程
1. **License导入操作**：点击"导入License"按钮 → 打开文件对话框 → 选择License文件 → 验证并导入 → 显示结果
2. **授权状态查看**：在功能模块树中选择模块 → 显示详细授权信息 → 查看权限级别和到期时间
3. **授权刷新操作**：点击"刷新状态"按钮 → 重新验证License → 更新功能模块状态 → 刷新界面显示

### 4.2 WPF交互反馈
- **成功状态**：使用绿色背景色和成功图标提示License导入成功，已授权模块显示绿色状态指示
- **错误处理**：使用红色背景色和错误图标提示License验证失败，显示具体错误信息
- **警告状态**：使用黄色背景色提示License即将到期，显示剩余天数
- **加载状态**：使用ProgressBar显示License验证进度，BusyIndicator显示处理状态

### 4.3 WPF快捷操作
- **键盘快捷键**：Ctrl+O导入License文件，F5刷新授权状态，Ctrl+I查看详细信息
- **右键菜单**：功能模块节点右键菜单包含"查看详情"、"导出授权信息"等操作
- **拖拽操作**：支持拖拽License文件到界面进行导入

## 5. MVVM技术实现方案
### 5.1 WPF架构设计
- **View层**：LicenseActivationView.xaml界面设计，包含License信息、功能模块状态、操作按钮等UI元素
- **ViewModel层**：LicenseActivationViewModel处理界面逻辑和数据绑定，管理授权状态、License信息等
- **Model层**：LicenseInfo、DeviceFingerprint、FunctionModule等数据模型
- **Service层**：LicenseService、DeviceFingerprintService、EncryptionService等业务服务

### 5.2 数据绑定策略
- **属性绑定**：License信息、功能模块状态、设备指纹等属性实现INotifyPropertyChanged接口
- **集合绑定**：使用ObservableCollection<FunctionModuleStatus>绑定功能模块状态列表
- **命令绑定**：操作按钮绑定到RelayCommand，支持CanExecute状态控制
- **转换器**：使用自定义IValueConverter转换授权状态颜色、到期时间显示格式等

### 5.3 WPF数据模型
- **LicenseInfo模型**：License文件信息，包含设备指纹、授权模块、有效期、License密钥等
- **DeviceFingerprint模型**：设备指纹信息，包含CPU ID、硬盘序列号、MAC地址等
- **FunctionModuleStatus模型**：功能模块状态，包含模块代码、名称、授权状态、权限级别等
- **AuthorizationResult模型**：授权验证结果，包含验证状态、错误信息、剩余天数等

### 5.4 服务接口设计
- **ILicenseService接口**：License验证服务，提供License文件解析、验证、管理等功能
- **IDeviceFingerprintService接口**：设备指纹服务，提供硬件信息收集和指纹生成功能
- **IEncryptionService接口**：加密解密服务，提供AES加密、RSA签名验证等功能
- **IAuthorizationService接口**：授权管理服务，提供功能模块权限控制和状态管理功能

## 6. WPF实现步骤
### 6.1 开发阶段划分
- **阶段一**：数据模型和服务接口实现(2天)
  - 创建LicenseInfo、DeviceFingerprint、FunctionModuleStatus等数据模型
  - 实现ILicenseService、IDeviceFingerprintService、IEncryptionService服务接口
  - 建立授权相关的数据库表结构和EF Core映射
- **阶段二**：核心服务实现(3天)
  - 实现设备指纹生成算法和硬件信息收集
  - 实现License文件解析、验证和数字签名验证
  - 实现AES加密解密和RSA签名验证功能
- **阶段三**：ViewModel层开发(2天)
  - 实现LicenseActivationViewModel核心逻辑
  - 设计License导入、验证、状态管理等功能
  - 实现命令绑定和属性通知机制
- **阶段四**：XAML界面设计(1天)
  - 设计LicenseActivationView主界面布局
  - 实现License信息显示、功能模块状态树、操作按钮等UI元素
  - 应用样式和模板，确保界面一致性

### 6.2 详细任务清单
- [ ] 创建LicenseInfo、DeviceFingerprint、FunctionModuleStatus数据模型类
- [ ] 实现ILicenseService接口和LicenseService服务类
- [ ] 实现IDeviceFingerprintService接口和DeviceFingerprintService服务类
- [ ] 实现IEncryptionService接口和EncryptionService服务类
- [ ] 实现设备指纹生成算法（CPU ID、硬盘序列号、MAC地址收集）
- [ ] 实现License文件JSON解析和AES解密功能
- [ ] 实现RSA数字签名验证功能
- [ ] 创建LicenseActivationViewModel类并实现核心属性和命令
- [ ] 实现License导入和验证逻辑
- [ ] 实现功能模块权限控制逻辑
- [ ] 设计LicenseActivationView XAML界面
- [ ] 实现数据绑定和命令绑定
- [ ] 添加值转换器和样式模板
- [ ] 实现文件对话框和消息提示功能
- [ ] 编写服务层单元测试
- [ ] 编写ViewModel单元测试
- [ ] 编写UI自动化测试
- [ ] 集成测试和安全性测试

## 7. WPF测试方案
### 7.1 ViewModel单元测试
- **属性测试**：验证LicenseInfo、FunctionModuleStatusList、DeviceFingerprint等属性的变更通知正确性
- **命令测试**：测试ImportLicenseCommand、RefreshStatusCommand、ViewDetailsCommand等命令的可执行性和执行结果
- **业务逻辑测试**：验证License验证逻辑、设备指纹生成逻辑、功能模块权限控制逻辑的正确性
- **异步操作测试**：测试License文件读取和验证的异步操作
- **错误处理测试**：验证License文件损坏、设备指纹不匹配、签名验证失败等异常情况的处理

### 7.2 服务层单元测试
- **设备指纹测试**：验证硬件信息收集的准确性和指纹生成的唯一性
- **加密解密测试**：测试AES加密解密和RSA签名验证的正确性
- **License解析测试**：验证License文件JSON解析和数据提取的准确性
- **权限控制测试**：测试功能模块权限控制逻辑的正确性

### 7.3 安全性测试
- **License篡改测试**：验证对篡改License文件的检测能力
- **设备指纹伪造测试**：测试对伪造设备指纹的防护能力
- **加密强度测试**：验证AES加密和RSA签名的安全强度
- **时间攻击测试**：测试License到期时间的准确性和防篡改能力

## 8. WPF验收标准
- **功能完整性**：所有规定功能正常工作，包括License导入、验证、权限控制、状态显示等
- **安全性要求**：License文件加密安全，设备指纹验证准确，数字签名验证可靠
- **界面美观性**：符合WPF设计规范和用户体验要求，界面布局合理、操作直观
- **性能要求**：License验证时间不超过3秒，设备指纹生成时间不超过1秒
- **稳定性要求**：连续运行无内存泄漏，异常情况下能够正确处理和恢复
- **兼容性**：在Windows 10/11系统上正常运行，支持不同硬件配置
- **可维护性**：代码结构清晰，遵循MVVM模式，注释充分，易于扩展和维护

## 9. WPF风险评估
### 9.1 技术风险
- **硬件信息收集复杂性**：不同硬件平台的信息收集方式可能不同，需要充分测试兼容性
- **加密算法实现**：AES加密和RSA签名的正确实现对安全性至关重要
- **License文件格式**：JSON格式的设计需要考虑扩展性和向后兼容性
- **权限控制复杂度**：动态控制功能模块权限的实现较为复杂

### 9.2 安全风险
- **私钥泄露风险**：RSA私钥的安全存储和使用需要特别注意
- **设备指纹绕过**：可能存在绕过设备指纹验证的攻击方式
- **License破解风险**：需要防范对License文件的逆向工程和破解
- **时间篡改风险**：系统时间被篡改可能影响License到期验证

### 9.3 时间风险
- **加密算法调试**：加密解密功能的调试和测试可能需要额外时间
- **硬件兼容性测试**：不同硬件平台的兼容性测试可能超出预期时间
- **安全性测试**：全面的安全性测试可能需要多次迭代

## 10. WPF后续优化方向
- **安全性增强**：增加更多的防篡改机制和安全验证手段
- **用户体验优化**：简化License导入流程，增加向导式操作
- **功能扩展**：支持在线License验证、自动更新、批量授权等功能
- **监控和审计**：增加License使用情况监控和操作审计日志
- **国际化支持**：支持多语言界面和错误信息本地化
