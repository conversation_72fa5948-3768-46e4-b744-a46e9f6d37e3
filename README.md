# 商用空调系统监控管理

## 项目概括
本项目旨在开发一个基于 WPF 和 .NET 8 的商用空调系统监控、调试和管理的单机离线版桌面应用程序。采用 MVVM 架构模式，为空调系统技术人员和维护工程师提供专业的设备监控、参数调试、数据分析和故障诊断功能。应用程序支持实时监控外机和内机运行状态，提供历史数据回放、设备远程控制、EEPROM读写、实时/历史曲线分析、故障报警和通信协议监听等核心功能。

## 技术选型
- **开发框架**: WPF (Windows Presentation Foundation)
- **.NET 版本**: .NET 8.0
- **架构模式**: MVVM (Model-View-ViewModel)
- **UI实现策略**: **仅使用原生WPF控件** (Button、TextBox、DataGrid、ListView、Canvas等)
- **界面设计**: 使用WPF控件的默认样式，进行必要的基础调整
- **数据访问**: Entity Framework Core 8.0
- **数据存储**: SQLite (本地数据库)
- **通信协议**: 串口通信 (System.IO.Ports)
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **消息传递**: CommunityToolkit.Mvvm
- **日志记录**: Serilog
- **数据绑定**: ObservableCollection, INotifyPropertyChanged
- **图表绘制**: 使用OxyPlot.Wpf图表库实现实时和历史数据图表（原生控件约束的例外）
- **版本控制**: Git
- **单元测试**: xUnit
- **部署方式**: 单机离线版，无需网络连接

## WPF 项目结构 / 模块划分
- `/Views/`: XAML 视图文件
  - `MainWindow.xaml`: 主窗口
  - `OutdoorUnitMonitorView.xaml`: 外机监控界面
  - `IndoorUnitMonitorView.xaml`: 内机监控界面
  - `DataPlaybackView.xaml`: 数据回放界面
  - `IndoorUnitControlView.xaml`: 内机控制界面
  - `EepromReadWriteView.xaml`: EEPROM读写界面
  - `RealTimeCurveView.xaml`: 实时曲线界面
  - `HistoryCurveView.xaml`: 历史曲线界面
  - `FaultAlarmView.xaml`: 故障报警界面
  - `FrameDataMonitorView.xaml`: 帧数据监听界面
  - `SerialPortConnectionView.xaml`: 串口连接配置界面
  - `LicenseActivationView.xaml`: 授权激活界面
- `/ViewModels/`: 视图模型类
  - `MainViewModel.cs`: 主窗口视图模型
  - `OutdoorUnitMonitorViewModel.cs`: 外机监控视图模型
  - `IndoorUnitMonitorViewModel.cs`: 内机监控视图模型
  - `DataPlaybackViewModel.cs`: 数据回放视图模型
  - `IndoorUnitControlViewModel.cs`: 内机控制视图模型
  - `EepromReadWriteViewModel.cs`: EEPROM读写视图模型
  - `RealTimeCurveViewModel.cs`: 实时曲线视图模型
  - `HistoryCurveViewModel.cs`: 历史曲线视图模型
  - `FaultAlarmViewModel.cs`: 故障报警视图模型
  - `FrameDataMonitorViewModel.cs`: 帧数据监听视图模型
  - `SerialPortConnectionViewModel.cs`: 串口连接配置视图模型
  - `LicenseActivationViewModel.cs`: 授权激活视图模型
- `/Models/`: 数据模型类
  - `DeviceInfo.cs`: 设备信息模型
  - `ParameterInfo.cs`: 参数信息模型
  - `ParameterValue.cs`: 参数值模型
  - `ModelParameterConfig.cs`: 机型参数配置模型
  - `DefaultParameterConfig.cs`: 默认参数配置模型
  - `AlarmInfo.cs`: 报警信息模型
  - `HistoryData.cs`: 历史数据模型
  - `CommunicationFrame.cs`: 通信帧数据模型
  - `SerialPortConfig.cs`: 串口配置模型
  - `LicenseInfo.cs`: 授权信息模型
  - `DeviceFingerprint.cs`: 设备指纹模型
  - `Protocol485Frame.cs`: 485协议帧数据模型
  - `ParameterFormat.cs`: 参数格式配置模型
  - `DevicePollingConfig.cs`: 设备轮询配置模型
  - `FunctionModule.cs`: 功能模块授权模型
- `/Services/`: 业务服务层
  - `CommunicationService.cs`: 通信服务
  - `DataCollectionService.cs`: 数据采集服务
  - `DatabaseService.cs`: 数据库服务
  - `AlarmService.cs`: 报警服务
  - `ConfigurationService.cs`: 配置管理服务
  - `SerialPortService.cs`: 串口通信管理服务
  - `LicenseService.cs`: 授权验证服务
  - `DeviceFingerprintService.cs`: 设备指纹生成服务
  - `EncryptionService.cs`: 加密解密服务
  - `Protocol485Parser.cs`: 485协议解析服务
  - `DevicePollingService.cs`: 设备轮询管理服务
  - `ParameterFormatService.cs`: 参数格式解析服务
- `/Controls/`: 自定义用户控件
  - `DynamicGridControl.cs`: 动态网格控件
  - `RealTimeChartControl.cs`: 实时图表控件
  - `ParameterDisplayControl.cs`: 参数显示控件
  - `SerialPortConfigControl.cs`: 串口配置控件
  - `LicenseStatusControl.cs`: 授权状态显示控件
- `/Converters/`: 值转换器
  - `ParameterValueConverter.cs`: 参数值格式转换器
  - `StatusToColorConverter.cs`: 状态颜色转换器
  - `BoolToVisibilityConverter.cs`: 布尔值可见性转换器
- `/Resources/`: 资源文件
  - `/Styles/`: 样式文件
    - `DefaultStyles.xaml`: 默认样式定义
  - `/Templates/`: 模板文件
  - `/Images/`: 图片资源
- `/Data/`: 数据访问层
  - `AirConditioningContext.cs`: EF Core数据上下文
  - `Repositories/`: 数据仓储接口和实现
- `/Utils/`: 工具类和辅助函数
  - `SerialPortHelper.cs`: 串口通信辅助类
  - `DataConverter.cs`: 数据转换工具
  - `LogHelper.cs`: 日志辅助类
- `App.xaml`: 应用程序定义
- `MainWindow.xaml`: 主窗口
- `AirMonitor.csproj`: 主项目文件
- `/LicenseGenerator/`: 注册机工具子项目
  - `/Views/`: 注册机界面文件
    - `MainWindow.xaml`: 注册机主窗口
    - `DeviceFingerprintView.xaml`: 设备指纹输入界面
    - `FunctionModuleSelectionView.xaml`: 功能模块选择界面
    - `LicenseGenerationView.xaml`: License生成界面
  - `/ViewModels/`: 注册机视图模型
    - `MainViewModel.cs`: 注册机主窗口视图模型
    - `DeviceFingerprintViewModel.cs`: 设备指纹视图模型
    - `FunctionModuleSelectionViewModel.cs`: 功能模块选择视图模型
    - `LicenseGenerationViewModel.cs`: License生成视图模型
  - `/Models/`: 注册机数据模型（共享主项目Models）
  - `/Services/`: 注册机服务层
    - `LicenseGeneratorService.cs`: License生成服务
    - `DigitalSignatureService.cs`: 数字签名服务
  - `LicenseGenerator.csproj`: 注册机项目文件
- `AirMonitor.sln`: 解决方案文件

## 核心功能模块 / 界面详解
- `外机监控模块`: 基于485总线主从轮询模式实时监控外机设备运行状态和参数，串口连接成功后自动开始设备扫描和参数监控，支持主外机+多子外机的动态设备发现，提供自定义轻量级网格控件显示外机设备层次结构和实时参数，支持2字节参数值的多格式解析和显示
- `内机监控模块`: 实时监控内机运行状态和参数，支持多内机同时监控，提供参数异常检测和状态指示
- `数据回放模块`: 历史数据查询和回放功能，支持时间范围选择、数据筛选和回放速度控制
- `内机控制模块`: 远程控制内机运行参数，包括温度设定、模式切换、风速调节等操作
- `EEPROM读写模块`: 设备存储器数据读取和写入，支持参数备份、恢复和批量配置
- `实时曲线模块`: 实时参数变化趋势图表显示，基于原生WPF控件绘制，支持多参数同时显示和缩放操作
- `历史曲线模块`: 历史数据趋势分析图表，支持时间段选择、参数对比和数据导出
- `故障报警模块`: 设备故障检测、报警和日志记录，提供报警级别分类、声音提示和处理记录
- `帧数据监听模块`: 通信协议数据包监听和分析，支持数据包解析、协议验证和通信诊断
- `串口连接模块`: 串口通信连接管理，支持串口参数配置、连接状态监控和自动重连机制
- `离线授权激活模块`: 基于设备指纹的离线授权系统，支持License文件导入、功能模块授权控制和授权状态显示
- `注册机工具`: 独立的License生成工具，支持设备指纹验证、功能模块选择、授权期限设置和加密License文件生成

## 数据模型设计
- **DeviceInfo**: { Id (int, PK), DeviceAddress (byte), DeviceName (string), DeviceType (enum), DeviceRole (enum), IsOnline (bool), LastUpdateTime (DateTime), ModelType (string), IsMasterDevice (bool), ParentDeviceId (int?) }
- **ParameterInfo**: { Id (int, PK), ParameterCode (string), ParameterName (string), Unit (string), DataType (enum), DisplayFormat (string), DisplayOrder (int) }
- **ParameterValue**: { Id (int, PK), DeviceId (int, FK), ParameterCode (string), ParameterIndex (byte), RawValue (ushort), DisplayValue (string), ValueFormat (enum), BitMask (ushort?), Timestamp (DateTime) }
- **ModelParameterConfig**: { Id (int, PK), ModelType (string), ParameterCode (string), IsVisible (bool), DisplayOrder (int), DisplayName (string) }
- **DefaultParameterConfig**: { Id (int, PK), ParameterCode (string), IsVisible (bool), DisplayOrder (int), DisplayName (string) }
- **AlarmInfo**: { Id (int, PK), DeviceId (int, FK), AlarmCode (string), AlarmLevel (enum), AlarmMessage (string), OccurTime (DateTime), ClearTime (DateTime?), IsCleared (bool) }
- **HistoryData**: { Id (int, PK), DeviceId (int, FK), ParameterCode (string), Value (string), RecordTime (DateTime) }
- **CommunicationFrame**: { Id (int, PK), Direction (enum), FrameData (byte[]), ParsedData (string), Timestamp (DateTime), IsValid (bool) }
- **Protocol485Frame**: { Id (int, PK), FrameType (enum), Header (byte), SourceAddress (byte), TargetAddress (byte), CommandCode (byte), MessageLength (byte), ParameterIndex (byte?), ParameterCount (byte?), ParameterValues (byte[]), DataArea (byte[]), CRC16 (ushort), IsValid (bool), ParsedTime (DateTime) }
- **SerialPortConfig**: { Id (int, PK), PortName (string), BaudRate (int), DataBits (int), StopBits (enum), Parity (enum), IsDefault (bool) }
- **LicenseInfo**: { Id (int, PK), DeviceFingerprint (string), AuthorizedModules (string), ExpiryDate (DateTime), LicenseKey (string), IsActive (bool) }
- **DeviceFingerprint**: { CpuId (string), HardDiskSerial (string), MacAddress (string), MotherboardSerial (string), FingerprintHash (string) }
- **FunctionModule**: { Id (int, PK), ModuleCode (string), ModuleName (string), IsAuthorized (bool), AuthorizationLevel (enum) }

## WPF 架构设计
- **View层**: 负责用户界面展示，使用XAML定义界面布局和样式，严格使用原生WPF控件，保持默认样式
- **ViewModel层**: 作为View和Model之间的桥梁，处理界面逻辑和数据绑定，实现INotifyPropertyChanged接口
- **Model层**: 定义业务数据模型和业务逻辑，包括设备信息、参数配置、历史数据等
- **Service层**: 提供数据访问、通信协议、数据采集、报警处理等业务服务
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection管理对象生命周期和依赖关系
- **消息传递**: 使用CommunityToolkit.Mvvm实现ViewModel之间的消息通信
- **数据持久化**: 使用Entity Framework Core和SQLite实现本地数据存储
- **授权验证**: 集成设备指纹识别和License验证机制，控制功能模块的访问权限
- **串口通信**: 统一的串口连接管理，支持多种串口参数配置和连接状态监控

## 界面设计规范 (基于原生WPF控件)
- **设计理念**: 使用WPF控件的默认样式，保持标准Windows应用程序外观，专注功能实现
- **色彩方案**: 使用系统默认颜色（SystemColors），定义少量应用程序级别颜色用于状态指示
- **字体规范**: 使用系统默认字体（SystemFonts），统一应用程序字体设置
- **原生控件使用**: 保持Button、TextBox、DataGrid、ListView、Canvas等控件的默认外观
- **布局原则**: 使用Grid、StackPanel、DockPanel等原生布局控件，遵循标准布局规范
- **简化设计**: 避免复杂的样式定制和动画效果，专注功能实现和数据展示
- **自定义控件**: 基于原生控件组合实现专用的监控界面控件，如动态网格控件和实时图表控件
- **数据可视化**: 使用OxyPlot.Wpf图表库实现专业的实时和历史数据图表显示
- **授权控制**: 基于License文件的功能模块访问控制，支持离线授权验证
- **串口管理**: 统一的串口连接配置和状态管理界面

## 自定义控件设计规范

### 动态网格控件 (DynamicGridControl)
**功能描述**: 专用于显示485总线设备参数的自定义网格控件，支持主从设备层次结构和动态列生成

**485总线设备布局**:
```
外机监控界面 (无边框网格布局)
┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐
│ 参数列  │  主机   │  子机1  │  子机2  │  子机3  │ 参数列  │  主机   │  子机1  │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ 参数1   │ 参数值1 │ 参数值1 │ 参数值1 │ 参数值1 │ 参数6   │ 参数值6 │ 参数值6 │
│ 参数2   │ 参数值2 │ 参数值2 │ 参数值2 │ 参数值2 │ 参数7   │ 参数值7 │ 参数值7 │
│ 参数3   │ 参数值3 │ 参数值3 │ 参数值3 │ 参数值3 │ 参数8   │ 参数值8 │ 参数值8 │
│ 参数4   │ 参数值4 │ 参数值4 │ 参数值4 │ 参数值4 │ 参数9   │ 参数值9 │ 参数值9 │
│ 参数5   │ 参数值5 │ 参数值5 │ 参数值5 │ 参数值5 │ 参数10  │ 参数值10│ 参数值10│
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
```

**技术实现**:
- **基础容器**: 使用原生Grid控件作为布局基础
- **设备扫描**: 基于485总线轮询结果动态发现主设备和从设备
- **动态列管理**: 根据设备地址动态创建设备列(主机、子机1、子机2等)
- **参数配置**: 根据主控机型从ModelParameterConfig表查询显示参数
- **多格式解析**: 支持2字节参数值的单一参数、位域组合、字节组合、混合格式、枚举值等多种格式
- **实时更新**: 通过485协议轮询获取参数值，使用INotifyPropertyChanged实现界面更新
- **设备状态**: 显示设备在线状态、通信质量和最后更新时间
- **性能优化**: 实现虚拟化滚动，仅渲染可见区域的控件

### 实时图表控件 (RealTimeChartControl)
**功能描述**: 基于OxyPlot.Wpf实现的专业实时数据图表控件

**技术实现**:
- **图表基础**: 使用OxyPlot.Wpf.PlotView作为图表容器
- **图表类型**: 支持线性图、散点图、柱状图等多种图表类型
- **数据绑定**: 通过ViewModel提供PlotModel，使用ObservableCollection实现数据变化通知
- **实时更新**: 支持高频数据更新和平滑动画过渡
- **交互功能**: 内置缩放、平移、数据点提示等专业图表交互
- **性能优化**: OxyPlot内置的数据虚拟化和渲染优化

### 串口连接控件 (SerialPortConfigControl)
**功能描述**: 串口连接配置和状态管理的专用控件

**技术实现**:
- **基础容器**: 使用原生Grid和StackPanel布局
- **配置界面**: ComboBox选择端口、波特率等参数
- **状态显示**: 使用TextBlock和Ellipse显示连接状态
- **操作按钮**: 连接/断开按钮，使用Command绑定
- **事件通知**: 通过消息传递机制通知连接状态变化

### 授权状态控件 (LicenseStatusControl)
**功能描述**: 显示授权状态和功能模块权限的控件

**技术实现**:
- **状态显示**: 使用TreeView显示功能模块授权层次结构
- **权限指示**: 通过图标和颜色显示各模块授权状态
- **到期提醒**: 显示License到期时间和剩余天数
- **操作功能**: 提供License导入和刷新功能

## 485通信协议设计

### 协议规范
#### 协议格式1 - 参数读写协议
```
| 头码 | 源地址 | 目标地址 | 命令码 | 报文长度 | 参数索引号idx | 参数个数 | 参数值1 | 参数值2 | ... | CRC16 |
|------|--------|----------|--------|----------|---------------|----------|---------|---------|-----|-------|
| 1字节| 1字节  | 1字节    | 1字节  | 1字节    | 1字节         | 1字节    | 2字节   | 2字节   | ... | 2字节 |
```

#### 协议格式2 - 数据传输协议
```
| 头码 | 源地址 | 目标地址 | 命令码 | 报文长度 | 数据区 | CRC16 |
|------|--------|----------|--------|----------|--------|-------|
| 1字节| 1字节  | 1字节    | 1字节  | 1字节    | N字节  | 2字节 |
```

### 总线通信架构
```
主设备(外机1) ←→ 从设备1(子外机) ←→ 从设备2(内机1) ←→ 从设备3(内机2) ←→ ...
     ↑
   监控软件(上位机)
```

### 通信流程 (主从轮询模式)
- **设备点播**: 主设备(外机1)轮流点播各从设备，获取设备状态
- **状态回复**: 从设备收到点播后，回复当前运行状态和参数
- **数据传输**: 使用格式2协议传输配置数据或固件升级
- **监控介入**: 上位机监控软件监听总线通信，不干扰主从通信

### 参数值结构 (2字节多格式组合)
每个2字节参数值支持多种数据格式：
- **单一参数**: 16位整数值 (如温度、压力数值)
- **位域组合**: 16个独立的布尔状态位
- **字节组合**: 高字节+低字节表示不同含义
- **混合格式**: 部分位表示状态，部分位表示数值
- **枚举值**: 设备模式、故障代码等枚举类型

### 协议解析实现
- **帧识别**: 基于头码和报文长度进行帧边界识别
- **CRC16校验**: 使用标准CRC16算法验证数据完整性
- **参数解析**: 根据参数索引号和配置表解析参数值格式
- **设备管理**: 基于源地址和目标地址进行设备识别和状态管理
- **错误处理**: 完善的通信错误检测、重试和恢复机制

## 离线授权系统设计
### 设备指纹生成
- **硬件信息收集**: CPU ID、硬盘序列号、MAC地址、主板序列号
- **指纹算法**: 使用SHA256哈希算法生成唯一设备标识
- **防篡改机制**: 多重硬件信息交叉验证，提高安全性

### License文件结构
- **基本信息**: 设备指纹、授权功能模块列表、有效期
- **数字签名**: 使用RSA算法对License内容进行数字签名
- **加密存储**: AES加密保护License文件内容
- **文件格式**: JSON格式，便于解析和扩展

### 功能模块授权控制
- **模块定义**: 外机监控、内机监控、数据回放、内机控制、EEPROM读写、实时曲线、历史曲线、故障报警、帧数据监听
- **权限级别**: 完全授权、试用版（功能限制）、禁用
- **动态控制**: 运行时根据License状态动态启用/禁用功能模块
- **界面反馈**: 未授权功能模块显示灰色状态和提示信息

## 注册机工具设计
### 核心功能
- **设备指纹输入**: 支持手动输入或从文件导入设备指纹信息
- **功能模块选择**: 可视化选择需要授权的功能模块和权限级别
- **授权期限设置**: 支持永久授权、试用期授权、自定义到期时间
- **License生成**: 生成加密的License文件并支持导出

### 安全机制
- **私钥保护**: 数字签名私钥安全存储，防止泄露
- **生成日志**: 记录所有License生成操作，便于审计
- **版本控制**: License文件版本管理，支持升级和兼容性检查

## 技术实现细节

### 485通信协议实现方案

#### Protocol485Parser服务实现
**核心功能**: 485协议帧解析、构建和CRC16校验
```csharp
public class Protocol485Parser
{
    // 协议格式1 - 参数读写协议解析
    public Protocol485Frame ParseParameterFrame(byte[] frameData);

    // 协议格式2 - 数据传输协议解析
    public Protocol485Frame ParseDataFrame(byte[] frameData);

    // 构建参数读取请求帧
    public byte[] BuildParameterReadFrame(byte sourceAddr, byte targetAddr, byte paramIndex, byte paramCount);

    // 构建参数写入请求帧
    public byte[] BuildParameterWriteFrame(byte sourceAddr, byte targetAddr, byte paramIndex, ushort[] paramValues);

    // CRC16校验计算
    public ushort CalculateCRC16(byte[] data, int offset, int length);

    // 帧有效性验证
    public bool ValidateFrame(byte[] frameData);
}
```

#### DevicePollingService实现
**核心功能**: 基于主从轮询模式的设备管理和数据采集
```csharp
public class DevicePollingService
{
    // 设备扫描 - 发现总线上的所有设备
    public async Task<List<DeviceInfo>> ScanDevicesAsync();

    // 轮询设备参数 - 按设备地址轮询获取参数
    public async Task<List<ParameterValue>> PollDeviceParametersAsync(byte deviceAddress);

    // 设备状态监控 - 检测设备在线状态
    public async Task MonitorDeviceStatusAsync();

    // 主从通信监听 - 监听主设备与从设备的通信
    public void StartBusMonitoring();

    // 设备层次结构构建 - 构建主设备+从设备的树形结构
    public DeviceHierarchy BuildDeviceHierarchy(List<DeviceInfo> devices);
}
```

#### ParameterFormatService实现
**核心功能**: 2字节参数值的多格式解析和显示转换
```csharp
public class ParameterFormatService
{
    // 单一参数解析 - 16位整数值
    public string ParseSingleParameter(ushort rawValue, ParameterFormat format);

    // 位域组合解析 - 16个独立布尔状态位
    public Dictionary<string, bool> ParseBitField(ushort rawValue, ParameterFormat format);

    // 字节组合解析 - 高字节+低字节不同含义
    public (string highByte, string lowByte) ParseByteCombo(ushort rawValue, ParameterFormat format);

    // 混合格式解析 - 部分位状态+部分位数值
    public MixedParameterValue ParseMixedFormat(ushort rawValue, ParameterFormat format);

    // 枚举值解析 - 设备模式、故障代码等
    public string ParseEnumValue(ushort rawValue, ParameterFormat format);

    // 根据机型获取参数配置
    public List<ParameterFormat> GetParameterFormats(string modelType);
}
```

### 外机监控模块MVVM实现方案

#### OutdoorUnitMonitorViewModel设计
```csharp
public class OutdoorUnitMonitorViewModel : ObservableObject
{
    // 设备层次结构
    public ObservableCollection<DeviceNode> DeviceHierarchy { get; set; }

    // 动态参数列表
    public ObservableCollection<ParameterColumn> ParameterColumns { get; set; }

    // 实时参数值矩阵 [设备][参数]
    public ObservableCollection<ObservableCollection<ParameterCell>> ParameterMatrix { get; set; }

    // 设备扫描命令
    public ICommand ScanDevicesCommand { get; }

    // 开始/停止监控命令
    public ICommand StartMonitoringCommand { get; }
    public ICommand StopMonitoringCommand { get; }

    // 参数配置命令
    public ICommand ConfigureParametersCommand { get; }

    // 485总线轮询逻辑
    private async Task StartPollingAsync()
    {
        while (_isMonitoring)
        {
            foreach (var device in DeviceHierarchy)
            {
                var parameters = await _pollingService.PollDeviceParametersAsync(device.Address);
                UpdateParameterMatrix(device.Address, parameters);
            }
            await Task.Delay(_pollingInterval);
        }
    }

    // 参数值更新逻辑
    private void UpdateParameterMatrix(byte deviceAddress, List<ParameterValue> parameters)
    {
        var deviceIndex = GetDeviceIndex(deviceAddress);
        foreach (var param in parameters)
        {
            var paramIndex = GetParameterIndex(param.ParameterCode);
            var displayValue = _formatService.ParseParameter(param.RawValue, param.ValueFormat);
            ParameterMatrix[deviceIndex][paramIndex].DisplayValue = displayValue;
            ParameterMatrix[deviceIndex][paramIndex].LastUpdate = DateTime.Now;
        }
    }
}
```

#### 动态网格控件数据绑定策略
```csharp
// 设备节点数据模型
public class DeviceNode : ObservableObject
{
    public byte Address { get; set; }
    public string DeviceName { get; set; }
    public DeviceType Type { get; set; }
    public bool IsMaster { get; set; }
    public bool IsOnline { get; set; }
    public DateTime LastUpdate { get; set; }
    public ObservableCollection<DeviceNode> Children { get; set; }
}

// 参数列数据模型
public class ParameterColumn : ObservableObject
{
    public string ParameterCode { get; set; }
    public string ParameterName { get; set; }
    public string Unit { get; set; }
    public int DisplayOrder { get; set; }
    public ParameterFormat Format { get; set; }
}

// 参数单元格数据模型
public class ParameterCell : ObservableObject
{
    public ushort RawValue { get; set; }
    public string DisplayValue { get; set; }
    public DateTime LastUpdate { get; set; }
    public bool IsValid { get; set; }
    public ParameterStatus Status { get; set; }
}
```

### 外机监控模块功能文档
**详细功能需求文档**: [WPF功能文档_外机监控模块.md](docs/WPF功能文档_外机监控模块.md)

**核心实现要点**:
- **485总线外机监控**: 基于主从轮询模式实时监控外机设备运行状态和参数，监控范围限定为外机设备
- **自动化监控流程**: 串口连接成功后自动开始外机设备扫描和参数监控，无需用户手动操作
- **动态外机发现**: 自动扫描485总线上的主外机和子外机，构建外机设备层次结构
- **多格式参数解析**: 支持2字节外机参数值的5种格式(单一参数、位域组合、字节组合、混合格式、枚举值)
- **自定义网格控件**: 实现轻量级动态网格显示外机设备层次结构和实时参数
- **简化用户界面**: 移除手动监控控制按钮，界面重点显示监控状态和外机参数数据
- **MVVM架构**: 严格遵循MVVM模式，使用ObservableCollection和INotifyPropertyChanged实现数据绑定

**开发优先级**: 高优先级，预估15个工作日完成
**技术依赖**: Protocol485Parser、DevicePollingService、ParameterFormatService等服务层支持

## 开发状态跟踪
| 功能模块/界面          | View状态 | ViewModel状态 | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------|----------|---------------|--------|--------------|--------------|-----------|
| 主窗口                 | 未开始   | 未开始        | AI     | 2024-12-10   |              |           |
| 外机监控模块           | 未开始   | 未开始        | AI     | 2024-12-12   |              |           |
| 内机监控模块           | 未开始   | 未开始        | AI     | 2024-12-14   |              |           |
| 数据回放模块           | 未开始   | 未开始        | AI     | 2024-12-16   |              |           |
| 内机控制模块           | 未开始   | 未开始        | AI     | 2024-12-18   |              |           |
| EEPROM读写模块         | 未开始   | 未开始        | AI     | 2024-12-20   |              |           |
| 实时曲线模块           | 未开始   | 未开始        | AI     | 2024-12-22   |              |           |
| 历史曲线模块           | 未开始   | 未开始        | AI     | 2024-12-24   |              |           |
| 故障报警模块           | 未开始   | 未开始        | AI     | 2024-12-26   |              |           |
| 帧数据监听模块         | 未开始   | 未开始        | AI     | 2024-12-28   |              |           |
| 动态网格控件           | 未开始   | 未开始        | AI     | 2024-12-15   |              |           |
| 实时图表控件           | 未开始   | 未开始        | AI     | 2024-12-25   |              |           |
| 数据模型层             | 未开始   | 未开始        | AI     | 2024-12-11   |              |           |
| 服务层                 | 未开始   | 未开始        | AI     | 2024-12-13   |              |           |
| 串口连接模块           | 未开始   | 未开始        | AI     | 2024-12-09   |              |           |
| 离线授权激活模块       | 未开始   | 未开始        | AI     | 2024-12-11   |              |           |
| 注册机工具项目         | 未开始   | 未开始        | AI     | 2024-12-30   |              |           |
| 设备指纹服务           | 未开始   | 未开始        | AI     | 2024-12-10   |              |           |
| License验证服务        | 未开始   | 未开始        | AI     | 2024-12-10   |              |           |
| Protocol485Parser服务  | 未开始   | 未开始        | AI     | 2024-12-12   |              |           |
| DevicePollingService   | 未开始   | 未开始        | AI     | 2024-12-12   |              |           |
| ParameterFormatService | 未开始   | 未开始        | AI     | 2024-12-12   |              |           |

## 代码检查与问题记录
[本部分用于记录WPF代码检查结果和开发过程中遇到的问题及其解决方案，包括XAML验证、数据绑定问题、性能优化等。]

## 环境设置与运行指南
### 开发环境要求
- **操作系统**: Windows 10/11
- **开发工具**: Visual Studio 2022 (17.8+)
- **.NET SDK**: .NET 8.0 SDK
- **数据库**: SQLite (无需额外安装)

### NuGet包依赖

#### 主项目 (AirMonitor)
```xml
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
<PackageReference Include="Serilog" Version="3.1.1" />
<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
<PackageReference Include="System.IO.Ports" Version="8.0.0" />
<PackageReference Include="OxyPlot.Wpf" Version="2.1.2" />
<PackageReference Include="System.Management" Version="8.0.0" />
<PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
```

#### 注册机工具 (LicenseGenerator)
```xml
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
<PackageReference Include="System.Management" Version="8.0.0" />
<PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
```

### 运行步骤
1. 克隆项目到本地
2. 使用Visual Studio 2022打开AirMonitor.sln
3. 还原NuGet包: `dotnet restore`
4. 编译解决方案: `dotnet build`
5. 运行主项目: 设置AirMonitor为启动项目，按F5运行
6. 运行注册机工具: 设置LicenseGenerator为启动项目，按F5运行

### 调试配置
- 设置启动项目为AirMonitor或LicenseGenerator
- 配置串口参数（如需要）
- 检查SQLite数据库连接字符串
- 准备测试用的License文件（可通过注册机工具生成）

### 首次运行配置
1. 运行注册机工具生成License文件
2. 将License文件导入主程序
3. 配置串口连接参数
4. 验证功能模块授权状态

## WPF性能优化
### 数据绑定优化
- 使用ObservableCollection进行集合绑定
- 实现INotifyPropertyChanged接口优化属性变更通知
- 避免频繁的UI线程更新，使用Dispatcher.BeginInvoke

### 界面渲染优化
- 对大数据量控件实现虚拟化滚动
- 使用数据模板优化重复控件渲染
- 避免复杂的样式和动画效果

### 内存管理
- 及时释放事件订阅和资源
- 使用弱引用避免内存泄漏
- 定期清理历史数据和缓存

### 通信性能优化
- 使用异步通信避免UI阻塞
- 实现数据缓存减少重复查询
- 优化数据采集频率和批量处理
- 串口连接池管理，避免频繁开关连接

### 图表性能优化
- 使用OxyPlot的内置性能优化功能
- 实现数据抽样，减少大数据量图表的渲染负担
- 异步数据更新，避免阻塞UI线程

### 授权验证优化
- License文件缓存，减少重复解析
- 设备指纹缓存，避免重复计算
- 异步授权验证，不影响程序启动速度

## 部署指南
### 发布配置
- 目标框架: net8.0-windows
- 部署模式: 自包含部署 (Self-Contained)
- 运行时标识符: win-x64
- 单文件发布: 是
- 修剪未使用代码: 是

### 发布命令

#### 主程序发布
```bash
dotnet publish AirMonitor -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true
```

#### 注册机工具发布
```bash
dotnet publish LicenseGenerator -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true
```

### 安装包制作
- 使用WiX Toolset或Inno Setup制作安装包
- 包含.NET 8运行时（自包含部署）
- 配置应用程序图标和版本信息
- 设置安装路径和快捷方式
- 分别打包主程序和注册机工具
- 包含示例License文件和使用说明

### 部署注意事项
- 主程序和注册机工具应分开部署
- 注册机工具仅提供给授权管理员使用
- 确保License文件的安全传输和存储
- 提供详细的授权激活操作手册