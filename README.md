# AirMonitor - 商用空调系统监控管理

## 项目概括
本项目旨在开发一个基于 WPF 和 .NET 8 的商用空调系统监控管理桌面应用程序，用于空调系统的实时监控、调试和管理。采用 MVVM 架构模式，提供直观的用户界面和流畅的用户体验。项目采用双项目架构设计，包含主监控程序和独立的License授权工具，支持离线运行和完整的权限管理体系。

## 技术选型
- **开发框架**: WPF (Windows Presentation Foundation)
- **.NET 版本**: .NET 8.0
- **架构模式**: MVVM (Model-View-ViewModel)
- **UI实现策略**: **仅使用原生WPF控件** (Button、TextBox、DataGrid、ListView等)
- **界面设计**: 使用WPF控件的默认样式，进行必要的基础调整
- **数据访问**: Entity Framework Core + SQLite
- **数据存储**: SQLite (会话数据库) + 配置文件
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **消息传递**: CommunityToolkit.Mvvm
- **加密算法**: AES-256 + RSA数字签名
- **串口通信**: System.IO.Ports
- **版本控制**: Git
- **其他工具**: NUnit (测试), Serilog (日志), Newtonsoft.Json (配置)

## 双项目架构设计

### 项目结构总览
```
AirMonitor/
├── AirMonitor/                    # 主应用程序项目
│   ├── Views/                     # XAML 视图文件
│   ├── ViewModels/                # 视图模型类
│   ├── Models/                    # 数据模型类
│   ├── Services/                  # 业务服务层
│   ├── Controls/                  # 自定义用户控件
│   ├── Converters/                # 值转换器
│   ├── Resources/                 # 资源文件
│   ├── Data/                      # 数据访问层
│   ├── Utils/                     # 工具类和辅助函数
│   └── AirMonitor.csproj          # 主项目文件
├── LicenseGenerator/              # License生成工具项目
│   ├── Views/                     # 注册机界面
│   ├── ViewModels/                # 注册机视图模型
│   ├── Models/                    # License相关模型
│   ├── Services/                  # License生成服务
│   └── LicenseGenerator.csproj    # 注册机项目文件
├── AirMonitor.Shared/             # 共享类库项目
│   ├── License/                   # License验证相关
│   ├── Security/                  # 加密和安全相关
│   ├── Hardware/                  # 硬件指纹采集
│   ├── Models/                    # 共享数据模型
│   └── AirMonitor.Shared.csproj   # 共享库项目文件
├── AirMonitor.Tests/              # 单元测试项目
└── AirMonitor.sln                 # 解决方案文件
```

### 项目依赖关系
- **AirMonitor** → 依赖 → **AirMonitor.Shared**
- **LicenseGenerator** → 依赖 → **AirMonitor.Shared**
- **AirMonitor.Tests** → 依赖 → **AirMonitor** + **AirMonitor.Shared**

## WPF 项目结构 / 模块划分

### AirMonitor 主项目结构
- `/Views/`: XAML 视图文件
  - `MainWindow.xaml`: 主窗口
  - `OutdoorUnitMonitorView.xaml`: 外机监控界面
  - `IndoorUnitMonitorView.xaml`: 内机监控界面
  - `DataPlaybackView.xaml`: 数据回放界面
  - `IndoorUnitControlView.xaml`: 内机控制界面
  - `EepromReadWriteView.xaml`: EEPROM读写界面
  - `RealTimeCurveView.xaml`: 实时曲线界面
  - `HistoryCurveView.xaml`: 历史曲线界面
  - `AlarmManagementView.xaml`: 故障报警界面
  - `FrameDataMonitorView.xaml`: 帧数据监听界面
  - `LicenseActivationView.xaml`: License激活界面
- `/ViewModels/`: 视图模型类
  - `MainViewModel.cs`: 主窗口视图模型
  - `OutdoorUnitMonitorViewModel.cs`: 外机监控视图模型
  - `IndoorUnitMonitorViewModel.cs`: 内机监控视图模型
  - `DataPlaybackViewModel.cs`: 数据回放视图模型
  - `IndoorUnitControlViewModel.cs`: 内机控制视图模型
  - `EepromReadWriteViewModel.cs`: EEPROM读写视图模型
  - `RealTimeCurveViewModel.cs`: 实时曲线视图模型
  - `HistoryCurveViewModel.cs`: 历史曲线视图模型
  - `AlarmManagementViewModel.cs`: 故障报警视图模型
  - `FrameDataMonitorViewModel.cs`: 帧数据监听视图模型
  - `LicenseActivationViewModel.cs`: License激活视图模型
- `/Models/`: 数据模型类
  - `DeviceInfo.cs`: 设备信息模型
  - `ParameterInfo.cs`: 参数信息模型
  - `ParameterValue.cs`: 参数值模型
  - `AlarmInfo.cs`: 报警信息模型
  - `SessionInfo.cs`: 会话信息模型
  - `CommunicationFrame.cs`: 通信帧模型
- `/Services/`: 业务服务层
  - `ILicenseService.cs`: License验证服务接口
  - `LicenseService.cs`: License验证服务实现
  - `IPermissionService.cs`: 权限管理服务接口
  - `PermissionService.cs`: 权限管理服务实现
  - `IDeviceCommunicationService.cs`: 设备通信服务接口
  - `DeviceCommunicationService.cs`: 设备通信服务实现
  - `IDataStorageService.cs`: 数据存储服务接口
  - `DataStorageService.cs`: 数据存储服务实现
  - `ISessionManagementService.cs`: 会话管理服务接口
  - `SessionManagementService.cs`: 会话管理服务实现
- `/Controls/`: 自定义用户控件
  - `LightweightDataGrid.xaml`: 轻量级网格控件
  - `RealTimeChart.xaml`: 实时图表控件
  - `ParameterDisplayPanel.xaml`: 参数显示面板
- `/Converters/`: 值转换器
  - `BooleanToVisibilityConverter.cs`: 布尔值到可见性转换器
  - `PermissionToVisibilityConverter.cs`: 权限到可见性转换器
  - `ParameterValueConverter.cs`: 参数值格式转换器
- `/Resources/`: 资源文件
  - `/Styles/`: 样式文件
    - `DefaultStyles.xaml`: 默认样式定义
  - `/Images/`: 图片资源
- `/Data/`: 数据访问层
  - `AirMonitorDbContext.cs`: 数据库上下文
  - `Entities/`: 数据库实体类
  - `Repositories/`: 数据仓储接口和实现
- `/Utils/`: 工具类和辅助函数
  - `ConfigurationHelper.cs`: 配置文件助手
  - `LoggingHelper.cs`: 日志记录助手
  - `SerialPortHelper.cs`: 串口通信助手
- `App.xaml`: 应用程序定义
- `MainWindow.xaml`: 主窗口
- `AirMonitor.csproj`: 项目文件

### LicenseGenerator 项目结构
- `/Views/`: 注册机界面
  - `MainWindow.xaml`: 主窗口
  - `HardwareInfoView.xaml`: 硬件信息显示
  - `PermissionConfigView.xaml`: 权限配置界面
  - `LicenseGenerationView.xaml`: License生成界面
- `/ViewModels/`: 注册机视图模型
  - `MainViewModel.cs`: 主窗口视图模型
  - `HardwareInfoViewModel.cs`: 硬件信息视图模型
  - `PermissionConfigViewModel.cs`: 权限配置视图模型
  - `LicenseGenerationViewModel.cs`: License生成视图模型
- `/Models/`: License相关模型
  - `HardwareFingerprint.cs`: 硬件指纹模型
  - `PermissionMatrix.cs`: 权限矩阵模型
  - `LicenseInfo.cs`: License信息模型
- `/Services/`: License生成服务
  - `ILicenseGenerationService.cs`: License生成服务接口
  - `LicenseGenerationService.cs`: License生成服务实现
  - `IHardwareInfoService.cs`: 硬件信息服务接口
  - `HardwareInfoService.cs`: 硬件信息服务实现

### AirMonitor.Shared 共享类库结构
- `/License/`: License验证相关
  - `LicenseValidator.cs`: License验证器
  - `LicenseFileManager.cs`: License文件管理
  - `PermissionManager.cs`: 权限管理器
- `/Security/`: 加密和安全相关
  - `AesEncryption.cs`: AES加密实现
  - `RsaSignature.cs`: RSA数字签名实现
  - `HashGenerator.cs`: 哈希生成器
- `/Hardware/`: 硬件指纹采集
  - `HardwareFingerprintCollector.cs`: 硬件指纹采集器
  - `CpuInfoCollector.cs`: CPU信息采集
  - `MotherboardInfoCollector.cs`: 主板信息采集
  - `HardDiskInfoCollector.cs`: 硬盘信息采集
  - `NetworkInfoCollector.cs`: 网络信息采集
- `/Models/`: 共享数据模型
  - `LicenseData.cs`: License数据模型
  - `PermissionItem.cs`: 权限项模型
  - `HardwareInfo.cs`: 硬件信息模型

## 核心功能模块 / 界面详解

### 1. 外机监控模块
- **功能描述**: 实时监控外机运行状态和参数，使用自定义轻量级网格控件显示多设备参数
- **权限控制**: 需要"外机监控"模块权限，包含"开始监控"、"停止监控"、"参数导出"按钮权限
- **技术特点**: 动态列生成、实时数据更新、性能优化的虚拟化显示

### 2. 内机监控模块
- **功能描述**: 实时监控内机运行状态和参数，支持多内机同时监控
- **权限控制**: 需要"内机监控"模块权限，包含监控控制和数据查看权限

### 3. 数据回放模块
- **功能描述**: 历史数据查询和回放功能，支持时间轴控制和播放速度调节
- **权限控制**: 需要"数据回放"模块权限，包含历史数据访问和导出权限
- **数据源**: 从历史会话数据库中查询和回放数据

### 4. 内机控制模块
- **功能描述**: 远程控制内机运行参数，支持参数设置和命令发送
- **权限控制**: 需要"内机控制"模块权限，包含参数修改和控制命令权限

### 5. EEPROM读写模块
- **功能描述**: 设备存储器数据读取和写入，支持数据备份和恢复
- **权限控制**: 需要"EEPROM读写"模块权限，包含读取和写入操作权限

### 6. 实时曲线模块
- **功能描述**: 实时参数变化趋势图表显示，基于原生WPF控件实现
- **权限控制**: 需要"实时曲线"模块权限，包含图表显示和配置权限

### 7. 历史曲线模块
- **功能描述**: 历史数据趋势分析图表，支持时间范围选择和多参数对比
- **权限控制**: 需要"历史曲线"模块权限，包含历史数据分析权限

### 8. 故障报警模块
- **功能描述**: 设备故障检测、报警和日志记录，支持报警规则配置
- **权限控制**: 需要"故障报警"模块权限，包含报警查看和处理权限

### 9. 帧数据监听模块
- **功能描述**: 通信协议数据包监听和分析，支持原始数据查看和协议解析
- **权限控制**: 需要"帧数据监听"模块权限，包含数据监听和分析权限

## 离线License授权系统设计

### License文件技术规范
- **文件格式**: 加密的二进制文件 (.lic扩展名)
- **加密算法**: AES-256 + RSA数字签名双重保护
- **文件结构**:
  ```
  License文件结构:
  ┌─────────────────────────────────────┐
  │ 文件头 (32字节)                      │
  │ - 魔数标识 (4字节)                   │
  │ - 版本号 (4字节)                     │
  │ - 数据长度 (4字节)                   │
  │ - 校验和 (4字节)                     │
  │ - 保留字段 (16字节)                  │
  ├─────────────────────────────────────┤
  │ 加密数据区 (变长)                    │
  │ - 软件版本信息                       │
  │ - 授权有效期                         │
  │ - 硬件指纹绑定                       │
  │ - 权限配置矩阵                       │
  │ - 扩展信息                           │
  ├─────────────────────────────────────┤
  │ 数字签名 (256字节)                   │
  │ - RSA-2048签名数据                   │
  └─────────────────────────────────────┘
  ```

### 权限配置矩阵设计
```json
{
  "modules": [
    {
      "moduleId": "OutdoorUnitMonitor",
      "moduleName": "外机监控",
      "enabled": true,
      "buttons": [
        {"buttonId": "StartMonitor", "buttonName": "开始监控", "enabled": true},
        {"buttonId": "StopMonitor", "buttonName": "停止监控", "enabled": true},
        {"buttonId": "ExportData", "buttonName": "数据导出", "enabled": false}
      ]
    },
    {
      "moduleId": "IndoorUnitControl",
      "moduleName": "内机控制",
      "enabled": false,
      "buttons": []
    }
  ]
}
```

### 硬件指纹绑定机制
- **CPU序列号**: 通过WMI查询处理器序列号
- **主板UUID**: 获取主板唯一标识符
- **硬盘序列号**: 查询系统盘硬盘序列号
- **MAC地址**: 获取主网卡MAC地址
- **组合算法**: 使用SHA-256生成综合硬件指纹

### LicenseGenerator注册机功能规范

#### 核心功能模块
- **硬件信息采集**: 自动检测目标机器的硬件特征信息
- **权限配置界面**: 可视化的权限矩阵配置工具（树形结构显示模块和按钮）
- **License生成**: 根据配置生成加密的License文件
- **License验证测试**: 验证生成的License文件的有效性和权限配置

#### 用户界面设计
- **硬件信息显示面板**: 只读显示CPU、主板、硬盘、网卡等硬件信息
- **权限配置树形控件**: 支持批量选择和权限继承的模块权限配置
- **授权期限设置控件**: 日期选择器、试用期选项、永久授权选项
- **License文件生成**: License文件生成和保存功能，支持批量生成

#### 权限配置界面设计
```
权限配置树形结构:
├── 外机监控模块 [✓]
│   ├── 开始监控 [✓]
│   ├── 停止监控 [✓]
│   └── 数据导出 [✗]
├── 内机监控模块 [✗]
├── 数据回放模块 [✓]
│   ├── 历史查询 [✓]
│   ├── 数据回放 [✓]
│   └── 数据导出 [✗]
├── 内机控制模块 [✗]
├── EEPROM读写模块 [✗]
├── 实时曲线模块 [✓]
├── 历史曲线模块 [✓]
├── 故障报警模块 [✓]
└── 帧数据监听模块 [✗]
```

## 数据存储架构设计

### 会话数据库创建策略
- **创建时机**: 每次串口连接建立时自动创建
- **命名规则**: `AirMonitor_Session_{yyyyMMdd_HHmmss}_{DeviceId}.db`
- **存储位置**: `%AppData%/AirMonitor/Sessions/`
- **生命周期**: 连接断开后数据库保持，用于历史数据查询

### 数据库表结构设计
```sql
-- 会话信息表
CREATE TABLE SessionInfo (
    SessionId TEXT PRIMARY KEY,
    StartTime DATETIME NOT NULL,
    EndTime DATETIME,
    DeviceId TEXT NOT NULL,
    DeviceName TEXT,
    ConnectionType TEXT,
    Status TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 设备数据表
CREATE TABLE DeviceData (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SessionId TEXT NOT NULL,
    Timestamp DATETIME NOT NULL,
    DeviceId TEXT NOT NULL,
    ParameterId TEXT NOT NULL,
    ParameterName TEXT,
    ParameterValue TEXT NOT NULL,
    DataType TEXT,
    Unit TEXT,
    FOREIGN KEY (SessionId) REFERENCES SessionInfo(SessionId)
);

-- 报警日志表
CREATE TABLE AlarmLog (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SessionId TEXT NOT NULL,
    Timestamp DATETIME NOT NULL,
    DeviceId TEXT NOT NULL,
    AlarmType TEXT NOT NULL,
    AlarmCode TEXT,
    AlarmDescription TEXT,
    Severity INTEGER,
    Status TEXT DEFAULT 'Active',
    AcknowledgedAt DATETIME,
    FOREIGN KEY (SessionId) REFERENCES SessionInfo(SessionId)
);

-- 通信日志表
CREATE TABLE CommunicationLog (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SessionId TEXT NOT NULL,
    Timestamp DATETIME NOT NULL,
    Direction TEXT NOT NULL, -- 'Send' or 'Receive'
    FrameType TEXT,
    RawData BLOB,
    ParsedData TEXT,
    IsValid BOOLEAN,
    FOREIGN KEY (SessionId) REFERENCES SessionInfo(SessionId)
);
```

### 数据回放功能集成
- **历史会话管理**: 会话数据库列表管理和元数据索引
- **时间范围查询**: 按时间范围查询和筛选历史数据
- **回放控制**: 数据回放时的时间轴控制和播放速度调节
- **界面切换**: 回放数据与实时数据的界面切换机制
- **数据导出**: 支持选定时间范围的数据导出功能

## WPF 架构设计

### MVVM模式实现
- **View层**: 负责用户界面展示，使用XAML定义界面布局和样式，严格使用原生WPF控件
- **ViewModel层**: 作为View和Model之间的桥梁，处理界面逻辑和数据绑定，集成权限验证
- **Model层**: 定义业务数据模型和业务逻辑
- **Service层**: 提供数据访问、业务服务、License验证、权限管理等功能
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection管理对象生命周期和依赖关系

### 权限验证集成方案
```csharp
// ViewModel中的权限控制示例
public class OutdoorUnitMonitorViewModel : ViewModelBase
{
    private readonly IPermissionService _permissionService;

    public ICommand StartMonitorCommand { get; }
    public ICommand ExportDataCommand { get; }

    public OutdoorUnitMonitorViewModel(IPermissionService permissionService)
    {
        _permissionService = permissionService;

        StartMonitorCommand = new RelayCommand(
            execute: StartMonitor,
            canExecute: () => _permissionService.HasPermission("OutdoorUnitMonitor", "StartMonitor")
        );

        ExportDataCommand = new RelayCommand(
            execute: ExportData,
            canExecute: () => _permissionService.HasPermission("OutdoorUnitMonitor", "ExportData")
        );
    }
}
```

### 依赖注入配置
```csharp
// App.xaml.cs中的服务注册
public partial class App : Application
{
    private ServiceProvider _serviceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        var services = new ServiceCollection();

        // 注册服务
        services.AddSingleton<ILicenseService, LicenseService>();
        services.AddSingleton<IPermissionService, PermissionService>();
        services.AddSingleton<IDeviceCommunicationService, DeviceCommunicationService>();
        services.AddSingleton<IDataStorageService, DataStorageService>();
        services.AddSingleton<ISessionManagementService, SessionManagementService>();

        // 注册ViewModels
        services.AddTransient<MainViewModel>();
        services.AddTransient<OutdoorUnitMonitorViewModel>();
        services.AddTransient<IndoorUnitMonitorViewModel>();

        _serviceProvider = services.BuildServiceProvider();

        // License验证
        var licenseService = _serviceProvider.GetService<ILicenseService>();
        if (!licenseService.ValidateLicense())
        {
            // 显示License激活界面
            ShowLicenseActivationWindow();
            return;
        }

        // 启动主窗口
        var mainWindow = new MainWindow();
        mainWindow.DataContext = _serviceProvider.GetService<MainViewModel>();
        mainWindow.Show();

        base.OnStartup(e);
    }
}
```

### 自定义轻量级网格控件设计

#### 控件架构设计
- **基础容器**: 使用原生Grid控件作为布局基础
- **动态列管理**: 通过代码动态创建ColumnDefinition和RowDefinition
- **数据绑定**: 使用ObservableCollection绑定设备列表和参数列表
- **性能优化**: 实现虚拟化滚动，仅渲染可见区域的控件
- **权限集成**: 根据权限控制列的显示和编辑能力

#### 动态列生成实现
```csharp
public class LightweightDataGrid : UserControl
{
    public ObservableCollection<DeviceInfo> Devices { get; set; }
    public ObservableCollection<ParameterInfo> Parameters { get; set; }

    private Grid _mainGrid;
    private Dictionary<string, TextBlock> _cellControls;

    public void RefreshGrid()
    {
        _mainGrid.Children.Clear();
        _mainGrid.ColumnDefinitions.Clear();
        _mainGrid.RowDefinitions.Clear();

        // 创建列定义：参数列 + 设备列
        CreateColumnDefinitions();

        // 创建行定义：参数行
        CreateRowDefinitions();

        // 创建表头
        CreateHeaders();

        // 创建数据单元格
        CreateDataCells();
    }

    private void CreateColumnDefinitions()
    {
        // 参数名称列
        _mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });

        // 设备数据列
        foreach (var device in Devices)
        {
            _mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });
        }
    }
}
```

## 界面设计规范 (基于原生WPF控件)

### 设计理念
- **原生控件策略**: 使用WPF控件的默认样式，保持标准Windows应用程序外观
- **权限驱动界面**: 根据License权限动态显示/隐藏界面元素
- **简化设计**: 避免复杂的样式定制和动画效果，专注功能实现
- **一致性原则**: 统一的字体、颜色、间距和交互方式

### 色彩方案设计
- **主色调**: 使用SystemColors.ControlBrush作为主背景色
- **强调色**: SystemColors.HighlightBrush用于选中状态
- **文本色**: SystemColors.ControlTextBrush用于普通文本
- **警告色**: SystemColors.InfoBrush用于提示信息
- **错误色**: 自定义红色 #FFE74C3C 用于错误和报警

### 字体规范
- **标题字体**: SystemFonts.CaptionFontFamily, 14pt, Bold
- **正文字体**: SystemFonts.MessageFontFamily, 12pt, Normal
- **说明字体**: SystemFonts.StatusFontFamily, 10pt, Normal
- **数据字体**: Consolas, 11pt, Normal (用于数值显示)

### 布局规范
- **主窗口布局**: DockPanel + Menu + StatusBar + TabControl
- **模块界面布局**: Grid布局，统一的边距设置(Margin="10")
- **按钮布局**: 统一高度30px，最小宽度80px，间距10px
- **输入控件**: 统一高度25px，标签宽度100px

## 开发阶段优先级和任务分解

### 第一阶段：授权系统基础架构（预估2-3周）
**主要任务**:
- 设计License文件格式和加密算法
- 实现硬件指纹采集服务
- 开发License验证和权限管理服务
- 创建权限控制的MVVM基础架构

**详细任务清单**:
- [ ] 创建AirMonitor.Shared共享类库项目
- [ ] 实现AES-256加密和RSA数字签名算法
- [ ] 开发硬件指纹采集器（CPU、主板、硬盘、网卡）
- [ ] 设计License文件格式和序列化机制
- [ ] 实现LicenseValidator和PermissionManager
- [ ] 创建ILicenseService和IPermissionService接口
- [ ] 开发权限控制的Command和Converter
- [ ] 编写单元测试覆盖核心加密和验证逻辑

**技术难点**:
- 硬件指纹的稳定性和唯一性保证
- 加密算法的安全性和性能平衡
- 权限验证在MVVM中的无缝集成

**验收标准**:
- License文件可以正确生成、加密和验证
- 硬件指纹绑定机制工作正常
- 权限控制可以动态影响UI元素状态

### 第二阶段：LicenseGenerator工具开发（预估1-2周）
**主要任务**:
- 开发独立的注册机WPF应用程序
- 实现权限配置界面和License生成功能
- 完成License文件的生成、验证和测试工具

**详细任务清单**:
- [ ] 创建LicenseGenerator WPF项目
- [ ] 开发硬件信息显示界面
- [ ] 实现权限配置树形控件
- [ ] 开发License生成和保存功能
- [ ] 实现License验证测试功能
- [ ] 添加批量License生成功能
- [ ] 创建用户操作指南和帮助文档

**技术难点**:
- 权限配置界面的用户体验设计
- 树形控件的权限继承逻辑
- License文件的批量生成和管理

**验收标准**:
- 可以可视化配置所有模块和按钮权限
- 生成的License文件可以被主程序正确识别
- 工具界面友好，操作简单直观

### 第三阶段：数据存储系统（预估1-2周）
**主要任务**:
- 设计和实现会话数据库创建机制
- 开发数据存储和查询服务
- 实现历史数据管理和回放基础架构

**详细任务清单**:
- [ ] 设计SQLite数据库表结构
- [ ] 实现Entity Framework Core数据访问层
- [ ] 开发会话管理服务
- [ ] 实现数据存储服务
- [ ] 创建历史数据查询接口
- [ ] 开发数据回放控制逻辑
- [ ] 实现数据导出功能

**技术难点**:
- 高频数据写入的性能优化
- 数据库文件的自动管理和清理
- 历史数据的高效查询和索引

**验收标准**:
- 会话数据库可以自动创建和管理
- 实时数据可以高效存储
- 历史数据查询性能满足要求

### 第四阶段：主程序授权集成（预估2-3周）
**主要任务**:
- 在AirMonitor主程序中集成License验证
- 为9个核心监控模块添加权限控制
- 实现界面元素的动态权限控制

**详细任务清单**:
- [ ] 创建AirMonitor主项目结构
- [ ] 集成License验证到应用程序启动流程
- [ ] 实现权限驱动的主界面布局
- [ ] 开发License激活界面
- [ ] 为所有ViewModel添加权限控制
- [ ] 实现权限相关的值转换器
- [ ] 创建权限控制的样式和模板
- [ ] 添加权限变更的实时响应机制

**技术难点**:
- 权限控制的性能影响最小化
- 界面元素的动态显示/隐藏逻辑
- 权限验证失败的用户体验处理

**验收标准**:
- 应用程序启动时正确验证License
- 所有界面元素根据权限正确显示
- 权限不足时有友好的提示信息

### 第五阶段：业务功能模块开发（预估4-6周）
**主要任务**:
- 按优先级开发9个核心监控功能模块
- 集成数据存储和回放功能
- 完成自定义轻量级网格控件开发

**模块开发优先级**:
1. **外机监控模块** (1周) - 核心功能，包含自定义网格控件
2. **内机监控模块** (0.5周) - 复用外机监控的控件和逻辑
3. **帧数据监听模块** (0.5周) - 通信调试的基础功能
4. **故障报警模块** (1周) - 重要的安全监控功能
5. **数据回放模块** (1周) - 历史数据分析功能
6. **实时曲线模块** (1周) - 数据可视化功能
7. **历史曲线模块** (0.5周) - 复用实时曲线的逻辑
8. **内机控制模块** (0.5周) - 设备控制功能
9. **EEPROM读写模块** (0.5周) - 高级调试功能

**技术难点**:
- 自定义网格控件的性能优化
- 实时数据更新的界面响应性
- 多模块间的数据共享和通信

**验收标准**:
- 所有模块功能完整，界面友好
- 实时数据更新流畅，无明显延迟
- 权限控制在所有模块中正确工作

## 技术实现细节
[本部分将在后续开发每一个模块/功能时，自动填充该模块/功能的MVVM实现方案、XAML界面设计、数据绑定策略、关键代码片段说明等。]

## 开发状态跟踪
| 功能模块/界面           | View状态 | ViewModel状态 | Service状态 | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------|----------|---------------|-------------|--------|--------------|--------------|-----------|
| License授权系统基础架构  | 未开始   | 未开始        | 未开始      | AI     | 2024-01-15   |              | 第一阶段   |
| LicenseGenerator工具    | 未开始   | 未开始        | 未开始      | AI     | 2024-01-22   |              | 第二阶段   |
| 数据存储系统            | 未开始   | 未开始        | 未开始      | AI     | 2024-01-29   |              | 第三阶段   |
| 主程序授权集成          | 未开始   | 未开始        | 未开始      | AI     | 2024-02-05   |              | 第四阶段   |
| 外机监控模块            | 未开始   | 未开始        | 未开始      | AI     | 2024-02-12   |              | 第五阶段   |
| 内机监控模块            | 未开始   | 未开始        | 未开始      | AI     | 2024-02-19   |              | 第五阶段   |
| 数据回放模块            | 未开始   | 未开始        | 未开始      | AI     | 2024-02-26   |              | 第五阶段   |
| 内机控制模块            | 未开始   | 未开始        | 未开始      | AI     | 2024-03-05   |              | 第五阶段   |
| EEPROM读写模块          | 未开始   | 未开始        | 未开始      | AI     | 2024-03-12   |              | 第五阶段   |
| 实时曲线模块            | 未开始   | 未开始        | 未开始      | AI     | 2024-03-19   |              | 第五阶段   |
| 历史曲线模块            | 未开始   | 未开始        | 未开始      | AI     | 2024-03-26   |              | 第五阶段   |
| 故障报警模块            | 未开始   | 未开始        | 未开始      | AI     | 2024-04-02   |              | 第五阶段   |
| 帧数据监听模块          | 未开始   | 未开始        | 未开始      | AI     | 2024-04-09   |              | 第五阶段   |

## 代码检查与问题记录
[本部分用于记录WPF代码检查结果和开发过程中遇到的问题及其解决方案，包括XAML验证、数据绑定问题、性能优化、License验证问题等。]

## 环境设置与运行指南
### 开发环境要求
- **Visual Studio**: 2022 或更高版本
- **.NET SDK**: .NET 8.0 SDK
- **数据库**: SQLite (通过NuGet包管理)
- **版本控制**: Git

### NuGet包依赖
```xml
<!-- AirMonitor主项目 -->
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
<PackageReference Include="Serilog" Version="3.1.1" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="System.IO.Ports" Version="8.0.0" />

<!-- AirMonitor.Shared共享库 -->
<PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
<PackageReference Include="System.Management" Version="8.0.0" />

<!-- 测试项目 -->
<PackageReference Include="NUnit" Version="3.14.0" />
<PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
```

### 调试运行命令
```bash
# 克隆项目
git clone <repository-url>
cd AirMonitor

# 还原NuGet包
dotnet restore

# 编译解决方案
dotnet build

# 运行主程序
dotnet run --project AirMonitor

# 运行注册机
dotnet run --project LicenseGenerator

# 运行测试
dotnet test
```

## WPF性能优化
### 自定义网格控件优化
- **虚拟化滚动**: 仅渲染可见区域的控件，减少内存占用
- **增量更新**: 只刷新变化的单元格，避免全量重绘
- **数据绑定优化**: 使用OneWay绑定减少双向绑定开销
- **UI线程优化**: 数据处理在后台线程，UI更新在主线程

### 实时数据更新优化
- **批量更新**: 将多个参数变更合并为一次UI更新
- **更新频率控制**: 限制UI刷新频率，避免过度渲染
- **内存管理**: 及时释放不再使用的数据对象
- **异步处理**: 使用async/await处理耗时操作

## 部署指南
### 应用程序打包
- **发布配置**: Release模式，自包含部署
- **目标框架**: win-x64，支持Windows 10及以上版本
- **文件结构**: 主程序 + 注册机 + 配置文件 + 帮助文档
- **安装程序**: 使用WiX或Inno Setup创建安装包

### License文件分发流程
1. **客户信息收集**: 获取客户硬件信息和权限需求
2. **License生成**: 使用LicenseGenerator工具生成专用License
3. **文件分发**: 通过安全渠道分发License文件
4. **激活验证**: 客户端导入License文件并验证激活
5. **技术支持**: 提供License相关的技术支持服务
```