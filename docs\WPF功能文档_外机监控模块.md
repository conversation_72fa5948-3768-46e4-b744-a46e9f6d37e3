# WPF功能文档：外机监控模块

## 1. 功能概述
- **功能名称**：外机监控模块
- **功能描述**：基于自定义485通讯协议规范实现的外机实时监控功能，支持主从轮询模式的设备通信、动态设备发现、多格式参数解析和自定义网格控件显示。该模块专门监控485总线上的外机设备(主外机和子外机)的运行状态和参数，提供自动化的工业监控界面。系统在串口连接成功后自动开始设备扫描和参数监控，无需用户手动干预。
- **目标用户**：空调系统技术人员、维护工程师、系统调试人员
- **优先级**：高
- **预估工作量**：15个工作日
- **依赖的WPF技术**：数据绑定、命令绑定、自定义控件、定时器、异步编程、MVVM模式

## 2. WPF功能需求详解
### 2.1 核心功能点
- **485总线外机监控**：基于主从轮询模式实时监控外机运行状态和参数，支持协议格式1的参数读写通信，监控范围限定为外机设备
- **动态外机发现**：自动扫描485总线上的外机设备，识别主外机和子外机，构建外机设备层次结构
- **多格式参数解析**：支持2字节参数值的5种格式解析(单一参数、位域组合、字节组合、混合格式、枚举值)
- **自定义网格控件**：实现轻量级动态网格显示，支持外机设备列和参数列的动态生成和实时数据更新
- **外机状态管理**：显示外机设备在线状态、通信质量、最后更新时间和设备地址信息
- **参数配置管理**：根据外机机型从ModelParameterConfig表动态配置显示参数，支持参数显示顺序和格式设置
- **自动化监控流程**：串口连接成功后自动开始设备扫描和参数监控，无需用户手动操作

### 2.2 用户操作流程
1. **串口连接**：用户在串口连接模块中配置并连接485串口
2. **自动设备扫描**：串口连接成功后，系统自动开始485总线外机设备扫描
3. **自动监控启动**：设备扫描完成后，系统自动开始实时参数监控
4. **参数配置**：系统根据外机机型自动加载参数配置，用户可手动调整显示参数
5. **实时数据查看**：用户查看网格控件中实时显示的外机参数值和设备状态
6. **手动重新扫描**：用户可选择点击"扫描设备"按钮手动重新扫描外机设备（可选操作）

### 2.3 数据流和绑定规范
- **输入数据**：485总线通信数据、外机设备地址配置、参数格式配置、外机机型参数映射表
- **输出数据**：外机设备状态信息、实时参数值、通信质量统计、外机设备层次结构
- **数据绑定**：外机设备列表绑定到ObservableCollection<DeviceNode>，参数矩阵绑定到二维ObservableCollection
- **命令绑定**：手动扫描设备、参数配置等操作绑定到ICommand接口

## 3. WPF界面设计规范
### 3.1 XAML布局设计
- **主容器**：使用Grid作为主布局容器，分为工具栏区域、外机信息区域、参数监控区域、状态栏区域
- **工具栏区域**：使用StackPanel水平排列操作按钮(手动扫描设备、参数配置)，移除监控控制按钮
- **外机信息区域**：使用TreeView显示外机设备层次结构，包含外机地址、名称、在线状态等信息
- **参数监控区域**：使用自定义DynamicGridControl显示外机参数网格，支持动态列生成和实时数据更新
- **状态栏区域**：使用StatusBar显示自动监控状态、外机设备数量、通信质量等统计信息

### 3.2 视觉设计要求
- **主题风格**：使用WPF控件默认样式，保持标准Windows应用程序外观
- **色彩方案**：使用SystemColors定义的系统颜色，外机设备状态使用绿色(在线)、红色(离线)、黄色(通信异常)
- **字体规范**：使用SystemFonts.DefaultFont，外机参数值使用等宽字体确保对齐
- **间距规范**：控件间距使用8px网格系统，内边距4px，外边距8px

### 3.3 WPF控件设计
- **按钮控件**：使用标准Button控件，仅保留"扫描设备"和"参数配置"按钮，通过Command绑定操作
- **输入控件**：使用ComboBox选择外机机型，TextBox输入外机地址范围，CheckBox选择显示参数
- **数据展示控件**：使用自定义DynamicGridControl显示外机参数网格，TreeView显示外机设备层次结构
- **状态指示控件**：使用Ellipse显示外机在线状态，ProgressBar显示通信质量，TextBlock显示自动监控状态

## 4. WPF交互设计
### 4.1 用户操作流程
1. **自动监控流程**：串口连接成功 → 系统自动扫描外机设备 → 自动开始参数监控 → 实时更新参数网格
2. **手动设备扫描**：点击"扫描设备"按钮 → 显示扫描进度 → 更新外机设备列表 → 显示扫描结果
3. **参数配置操作**：点击"参数配置"按钮 → 打开配置对话框 → 选择显示参数 → 应用配置并刷新界面
4. **外机选择操作**：在外机设备树中选择设备 → 高亮对应的参数列 → 显示外机详细信息

### 4.2 WPF交互反馈
- **成功状态**：使用绿色背景色和成功图标提示操作成功，外机参数值正常更新时显示绿色状态指示
- **错误处理**：使用红色背景色和错误图标提示操作失败，外机通信异常时显示红色状态指示和错误信息
- **加载状态**：使用ProgressBar显示外机设备扫描进度，BusyIndicator显示数据加载状态
- **自动监控状态**：使用状态栏显示"自动监控中"、"设备扫描中"等自动化流程状态

### 4.3 WPF快捷操作
- **键盘快捷键**：F5手动刷新外机设备列表，Ctrl+P参数配置
- **右键菜单**：外机设备节点右键菜单包含"查看详情"、"重新连接"、"设置别名"等操作
- **拖拽操作**：支持拖拽调整外机参数列顺序，拖拽外机设备节点调整显示位置

## 5. MVVM技术实现方案
### 5.1 WPF架构设计
- **View层**：OutdoorUnitMonitorView.xaml界面设计，包含工具栏、设备树、参数网格、状态栏等UI元素
- **ViewModel层**：OutdoorUnitMonitorViewModel处理界面逻辑和数据绑定，管理设备列表、参数矩阵、监控状态等
- **Model层**：DeviceInfo、ParameterValue、Protocol485Frame等数据模型
- **Service层**：Protocol485Parser、DevicePollingService、ParameterFormatService等业务服务

### 5.2 数据绑定策略
- **属性绑定**：外机设备列表、参数矩阵、自动监控状态等属性实现INotifyPropertyChanged接口
- **集合绑定**：使用ObservableCollection<DeviceNode>绑定外机设备树，ObservableCollection<ParameterCell>绑定参数网格
- **命令绑定**：手动扫描、参数配置按钮绑定到RelayCommand，支持CanExecute状态控制
- **转换器**：使用自定义IValueConverter转换外机参数值格式、设备状态颜色、在线状态图标等

### 5.3 WPF数据模型
- **DeviceNode模型**：外机设备节点信息，包含地址、名称、类型、在线状态、子外机列表等
- **ParameterColumn模型**：外机参数列信息，包含参数代码、名称、单位、显示格式、显示顺序等
- **ParameterCell模型**：外机参数单元格信息，包含原始值、显示值、更新时间、有效性、状态等
- **AutoMonitoringStatus模型**：自动监控状态信息，包含自动监控状态、外机设备数量、通信质量、错误计数等

### 5.4 服务接口设计
- **IProtocol485Parser接口**：485协议解析服务，提供帧解析、构建、校验等功能
- **IOutdoorUnitPollingService接口**：外机设备轮询服务，提供外机扫描、参数轮询、状态监控等功能
- **IParameterFormatService接口**：参数格式服务，提供多格式外机参数解析和显示转换功能
- **IConfigurationService接口**：配置管理服务，提供外机参数配置、机型配置、显示配置等功能

## 6. WPF实现步骤
### 6.1 开发阶段划分
- **阶段一**：数据模型和服务接口实现(3天)
  - 创建外机专用的DeviceNode、ParameterColumn、ParameterCell等数据模型
  - 实现Protocol485Parser、OutdoorUnitPollingService、ParameterFormatService服务
  - 建立外机设备相关的数据库表结构和EF Core映射
- **阶段二**：ViewModel层开发(4天)
  - 实现OutdoorUnitMonitorViewModel核心逻辑，包含自动化监控流程
  - 设计外机设备扫描、参数轮询、状态管理等功能
  - 实现串口连接事件监听和自动启动监控机制
- **阶段三**：自定义控件开发(3天)
  - 开发外机专用的DynamicGridControl动态网格控件
  - 实现外机设备树控件和参数配置控件
  - 优化控件性能和用户体验
- **阶段四**：XAML界面设计(2天)
  - 设计OutdoorUnitMonitorView主界面布局，移除监控控制按钮
  - 实现简化的工具栏、外机设备树、参数网格、状态栏等UI元素
  - 应用样式和模板，确保界面一致性
- **阶段五**：自动化流程和数据绑定实现(2天)
  - 实现串口连接事件监听和自动监控启动逻辑
  - 实现View和ViewModel之间的数据绑定
  - 集成485通信协议和实时数据更新
- **阶段六**：测试与性能优化(1天)
  - 编写单元测试和集成测试，重点测试自动化流程
  - 性能优化和内存泄漏检查
  - 用户体验测试和界面调优

### 6.2 详细任务清单
- [ ] 创建外机专用的DeviceNode、ParameterColumn、ParameterCell数据模型类
- [ ] 实现IProtocol485Parser接口和Protocol485Parser服务类
- [ ] 实现IOutdoorUnitPollingService接口和OutdoorUnitPollingService服务类
- [ ] 实现IParameterFormatService接口和ParameterFormatService服务类
- [ ] 创建OutdoorUnitMonitorViewModel类并实现自动化监控逻辑
- [ ] 实现串口连接事件监听和自动启动机制
- [ ] 实现外机设备扫描逻辑和485总线通信
- [ ] 实现外机参数轮询逻辑和实时数据更新
- [ ] 开发外机专用的DynamicGridControl自定义控件
- [ ] 设计OutdoorUnitMonitorView XAML界面，移除监控控制按钮
- [ ] 实现数据绑定和简化的命令绑定
- [ ] 添加外机参数值转换器和样式模板
- [ ] 实现自动化流程的事件处理逻辑
- [ ] 编写ViewModel单元测试，重点测试自动化流程
- [ ] 编写UI自动化测试
- [ ] 性能优化和内存管理
- [ ] 用户体验测试和界面调优

## 7. WPF测试方案
### 7.1 ViewModel单元测试
- **属性测试**：验证OutdoorDeviceHierarchy、ParameterMatrix、AutoMonitoringStatus等属性的变更通知正确性
- **命令测试**：测试ScanOutdoorDevicesCommand、ConfigureParametersCommand等命令的可执行性和执行结果
- **自动化流程测试**：验证串口连接事件触发的自动设备扫描和监控启动逻辑
- **业务逻辑测试**：验证外机设备扫描逻辑、参数轮询逻辑、数据格式转换逻辑的正确性
- **异步操作测试**：测试485通信的异步操作和UI线程同步机制
- **错误处理测试**：验证外机通信异常、设备离线、数据解析错误等异常情况的处理

### 7.2 UI自动化测试
- **界面元素测试**：验证简化工具栏、外机设备树、参数网格、状态栏等控件的存在和可见性
- **交互测试**：模拟用户点击扫描按钮、选择外机设备、配置参数等操作，验证界面响应
- **数据绑定测试**：验证外机设备数据与界面显示的同步，参数值更新的实时性
- **自动化流程测试**：测试串口连接触发的自动监控启动，外机设备在线/离线状态变化
- **性能测试**：测试大量外机设备和参数时的界面响应速度和内存使用

### 7.3 集成测试
- **485通信测试**：使用模拟外机设备或真实外机设备测试485协议通信的完整流程
- **数据库集成测试**：验证外机参数配置、设备信息的数据库读写操作
- **服务集成测试**：测试ViewModel与各个Service之间的协作和数据传递
- **多外机设备测试**：测试主外机+多个子外机的监控场景
- **自动化流程集成测试**：验证串口连接到自动监控启动的完整流程
- **长时间运行测试**：验证长时间自动监控的稳定性和内存泄漏情况

## 8. WPF验收标准
- **功能完整性**：所有规定功能正常工作，包括自动外机设备扫描、实时监控、参数解析、状态显示等
- **自动化要求**：串口连接成功后能够自动启动外机设备扫描和参数监控，无需用户手动干预
- **界面美观性**：符合WPF设计规范和用户体验要求，界面布局合理、操作简化
- **性能要求**：支持至少10个外机设备同时监控，参数更新频率不低于1Hz，界面响应时间不超过100ms
- **稳定性要求**：连续自动运行24小时无崩溃，内存使用稳定，外机通信异常自动恢复
- **兼容性**：在Windows 10/11系统上正常运行，支持不同分辨率和DPI设置
- **可维护性**：代码结构清晰，遵循MVVM模式，注释充分，易于扩展和维护

## 9. WPF风险评估
### 9.1 技术风险
- **485通信复杂性**：自定义协议解析和多外机设备轮询的实现难度较高，需要充分测试通信稳定性
- **自动化流程复杂性**：串口连接事件监听和自动监控启动的时序控制较为复杂
- **实时性能问题**：高频外机数据更新可能导致UI线程阻塞，需要优化异步处理和数据绑定性能
- **自定义控件复杂度**：外机专用DynamicGridControl的动态列生成和数据绑定较为复杂，需要仔细设计
- **多格式参数解析**：2字节外机参数值的5种格式解析逻辑复杂，容易出现解析错误

### 9.2 时间风险
- **485协议调试时间**：外机协议解析和通信调试可能需要额外时间，特别是与真实外机设备联调
- **自动化流程开发**：串口事件监听和自动监控启动逻辑的开发和调试可能超出预期时间
- **自定义控件开发**：外机专用动态网格控件的开发和优化可能超出预期时间
- **性能优化时间**：外机实时数据更新的性能优化可能需要多次迭代

### 9.3 依赖风险
- **硬件设备依赖**：需要真实的485外机设备进行测试，外机设备可用性可能影响开发进度
- **协议文档依赖**：依赖准确的485协议文档，协议变更可能影响实现方案
- **串口模块依赖**：依赖串口连接模块的完成，串口连接事件机制需要先行实现

## 10. WPF后续优化方向
- **性能优化**：进一步提升大量外机设备监控时的渲染性能和内存使用效率
- **用户体验**：增加外机参数趋势图、报警提示、数据导出等功能
- **功能扩展**：支持外机设备参数写入、固件升级、批量配置等高级功能
- **自动化增强**：增加智能故障检测、自动重连、预测性维护等自动化功能
- **可视化增强**：添加外机设备拓扑图、3D外机模型、动态仪表盘等可视化元素
- **国际化支持**：支持多语言界面和外机参数名称本地化
